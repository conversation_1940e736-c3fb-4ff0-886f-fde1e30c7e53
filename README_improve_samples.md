# Translation Sample Improvement Tool

An interactive tool to build and refine translation examples that help maintain consistent style and quality across your entire translation project.

## 🎯 Purpose

This script helps you create a curated "translation memory" by:

1. **Picking random sentences** from your source text
2. **Translating them** using your current llama.cpp setup
3. **Allowing you to review and edit** translations interactively
4. **Building a database** of approved translation pairs
5. **Improving consistency** in future translations

## 🚀 Quick Start

```bash
# Start llama.cpp server
./startLLM.sh

# Run the improvement tool
python3 improve_samples.py --source in.txt --style style.txt --max_iterations 10

# Stop server when done
./stopLLM.sh
```

## 📋 How It Works

### 1. **Sentence Selection**
- Randomly picks sentences from your source text (5-50 words)
- Avoids duplicates by tracking previously processed sentences
- Filters out fragments, very short/long sentences, and punctuation-only text

### 2. **AI Translation**
- Uses your existing `translate3_llamacpp.py` translation logic
- Applies your style guide for consistency
- Uses the same model and parameters as your main translation workflow

### 3. **Interactive Review**
For each translation, you can:
- **[a] Approve** - Translation is good, current guidelines are sufficient (no new sample added)
- **[e] Edit** - Provide your preferred translation and add it to guidelines
- **[s] Skip** - Move to next sentence without saving
- **[q] Quit** - End the session

### 4. **Smart Guidelines Building**
- **Only edited translations** are added to `sample_source.txt` and `sample_target.txt`
- **Approved translations** indicate current guidelines are working (no addition needed)
- Uses existing guidelines to provide better initial translations
- Builds a focused database of your preferred translation corrections

## 🎨 Example Session

```
🔄 Iteration 1/10
🤖 Translating...

================================================================================
📖 ORIGINAL (Russian):
  Они стояли против света, лившегося с арены, потому представляли собой
  чёрные силуэты

🔄 AI TRANSLATION (English):
  They stood against the light streaming from the arena, so they appeared as
  black silhouettes.
================================================================================

What would you like to do?
  [a] Approve this translation
  [e] Edit/provide alternative translation
  [s] Skip this sentence
  [q] Quit

Your choice (a/e/s/q): e

✏️  Please provide your preferred translation:
(Press Enter on empty line to finish, or type 'cancel' to cancel)
  Bathed in the fierce illumination from the arena, their bodies were nothing
  more than sharp, black shapes.

✅ Your translation added to guidelines!
```

## 📊 Benefits

### **Consistency**
- Ensures similar phrases are translated the same way throughout the book
- Maintains character names, terminology, and style choices
- Reduces translation drift over long documents

### **Quality Control**
- Allows you to correct AI mistakes before they propagate
- Builds domain-specific vocabulary (gladiator/Roman terms)
- Captures your preferred tone and style nuances

### **Efficiency**
- Improves future translations by providing examples
- Reduces need for manual editing of large translations
- Creates a reusable translation memory for similar projects

## ⚙️ Command Line Options

```bash
python3 improve_samples.py [options]

Options:
  --source FILE           Source Russian text file (default: in.txt)
  --style FILE           Style guide file (default: style.txt)
  --sample_source FILE   Sample source file to append to (default: sample_source.txt)
  --sample_target FILE   Sample target file to append to (default: sample_target.txt)
  --model NAME           Model name for translation (default: gemma-2-27b-it)
  --max_iterations N     Maximum sentences to process (default: 10)
```

## 📈 Usage Strategies

### **Initial Setup** (First 20-30 samples)
```bash
# Build initial sample database
python3 improve_samples.py --max_iterations 30
```
Focus on:
- Character names and titles
- Key terminology (gladiator, arena, master, etc.)
- Dialogue style and tone
- Action descriptions

### **Ongoing Refinement** (5-10 samples per session)
```bash
# Regular improvement sessions
python3 improve_samples.py --max_iterations 10
```
Focus on:
- Phrases that appear frequently
- Complex sentences that AI struggles with
- Stylistic nuances you want to maintain

### **Quality Assurance** (Before major translations)
```bash
# Quick check before translating large sections
python3 improve_samples.py --max_iterations 5
```

## 🔍 Best Practices

### **When to Approve AI Translations**
- ✅ Translation is accurate and maintains the Roman gladiator style
- ✅ Terminology is consistent with your preferences
- ✅ Tone matches the dramatic, gritty atmosphere
- ✅ Character names and titles are correct

### **When to Edit Translations**
- ✏️ AI translation is too literal or awkward
- ✏️ Style doesn't match the Roman gladiator atmosphere
- ✏️ Better word choice would improve flow
- ✏️ Terminology needs to be more specific/dramatic

### **When to Skip**
- ⏭️ Sentence is too fragmentary or context-dependent
- ⏭️ Translation is acceptable but not worth adding to samples
- ⏭️ You're unsure and want to see more context first

## 📁 File Management

### **Sample Files Structure**
```
sample_source.txt:
[Original long sample text...]
---
Дойдя до колодца, Кар положил суму на землю
---
Но здесь и сейчас все поединки были смертельными

sample_target.txt:
[Original long sample translation...]
---
Having reached the well, Kar put the bag on the ground.
---
But here and now, all the duels were to the death.
```

**Note**: The script uses `---` delimiters for new samples while preserving backward compatibility with existing formats.

### **Backup Strategy**
```bash
# Backup your samples before major sessions
cp sample_source.txt sample_source_backup.txt
cp sample_target.txt sample_target_backup.txt
```

## 🔧 Integration with Main Translation

The samples you build are automatically used by `translate3_llamacpp.py`:

```bash
# Your improved samples will be used here
python3 translate3_llamacpp.py \
  --txt large_document.txt \
  --style style.txt \
  --output_txt output.txt \
  --sample_source sample_source.txt \
  --sample_target sample_target.txt
```

## 📊 Monitoring Progress

The script provides session summaries:
```
📊 SESSION SUMMARY
==============================
✅ Approved: 5
✏️  Edited: 3
⏭️  Skipped: 2
📝 Total samples added: 8
📚 Total samples in database: 45
```

Track your progress over time:
- **Target**: 50-100 high-quality samples for consistent translation
- **Quality over quantity**: Better to have fewer excellent samples than many mediocre ones
- **Regular sessions**: 5-10 samples per session works well

## 🎯 Expected Results

After building a solid sample database (50+ pairs), you should see:

- **More consistent terminology** across translations
- **Better style adherence** to your Roman gladiator theme
- **Fewer manual corrections** needed in final translations
- **Improved AI understanding** of your preferred style

This tool transforms your translation workflow from reactive editing to proactive style building!
