#!/bin/bash

# stopLLM.sh - Stop llama.cpp server gracefully
# Finds and stops llama-server processes

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🛑 Stopping llama.cpp server...${NC}"

# Find llama-server processes
PIDS=$(pgrep -f "llama-server")

if [ -z "$PIDS" ]; then
    echo -e "${YELLOW}ℹ️  No llama-server processes found running.${NC}"
    exit 0
fi

echo -e "${YELLOW}Found llama-server process(es): $PIDS${NC}"

# Try graceful shutdown first (SIGTERM)
echo -e "${BLUE}🔄 Attempting graceful shutdown...${NC}"
for PID in $PIDS; do
    echo -e "${YELLOW}Sending SIGTERM to process $PID${NC}"
    kill -TERM $PID 2>/dev/null
done

# Wait a few seconds for graceful shutdown
sleep 3

# Check if processes are still running
REMAINING_PIDS=$(pgrep -f "llama-server")

if [ -z "$REMAINING_PIDS" ]; then
    echo -e "${GREEN}✅ All llama-server processes stopped gracefully.${NC}"
    exit 0
fi

# If still running, force kill
echo -e "${YELLOW}⚠️  Some processes still running. Force killing...${NC}"
for PID in $REMAINING_PIDS; do
    echo -e "${YELLOW}Sending SIGKILL to process $PID${NC}"
    kill -KILL $PID 2>/dev/null
done

# Final check
sleep 1
FINAL_CHECK=$(pgrep -f "llama-server")

if [ -z "$FINAL_CHECK" ]; then
    echo -e "${GREEN}✅ All llama-server processes stopped.${NC}"
else
    echo -e "${RED}❌ Some processes may still be running: $FINAL_CHECK${NC}"
    echo -e "${YELLOW}You may need to manually kill them or restart your system.${NC}"
    exit 1
fi

# Also check if port 8080 is still in use
if lsof -Pi :8080 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  Port 8080 is still in use by another process.${NC}"
    echo -e "${YELLOW}Use 'lsof -i :8080' to identify the process.${NC}"
else
    echo -e "${GREEN}✅ Port 8080 is now available.${NC}"
fi
