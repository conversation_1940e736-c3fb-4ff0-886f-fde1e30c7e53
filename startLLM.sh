#!/bin/bash

# startLLM.sh - Start llama.cpp server with optimized parameters for translation
# Optimized for Apple Silicon M1 Max with Gemma 2 27B-IT model

# Configuration
MODEL_PATH="gemma-2-27b-it-Q4_K_M.gguf"
PORT=8080
HOST="0.0.0.0"

# Optimized parameters based on testing
CONTEXT_SIZE=8192           # Context window (increased for full style guide)
MAX_PREDICT=512            # Max prediction tokens
THREADS=8                  # All P-cores (M1 Max has 8 P-cores)
THREADS_BATCH=8            # Batch processing threads
BATCH_SIZE=2048            # Batch size for processing
UBATCH_SIZE=512            # Micro-batch size
PARALLEL_SLOTS=4           # Number of concurrent slots
FLASH_ATTN="--flash-attn"  # Enable flash attention for efficiency

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting llama.cpp server with optimized parameters...${NC}"
echo -e "${YELLOW}Model: ${MODEL_PATH}${NC}"
echo -e "${YELLOW}Port: ${PORT}${NC}"
echo -e "${YELLOW}Parallel slots: ${PARALLEL_SLOTS}${NC}"
echo -e "${YELLOW}Flash attention: Enabled${NC}"

# Check if model file exists
if [ ! -f "llama.cpp/$MODEL_PATH" ]; then
    echo -e "${RED}❌ Error: Model file not found at llama.cpp/${MODEL_PATH}${NC}"
    echo -e "${YELLOW}Please ensure the Gemma 2 27B-IT model is downloaded to the llama.cpp directory.${NC}"
    exit 1
fi

# Check if llama.cpp server binary exists
if [ ! -f "llama.cpp/build/bin/llama-server" ]; then
    echo -e "${RED}❌ Error: llama-server binary not found${NC}"
    echo -e "${YELLOW}Please build llama.cpp first:${NC}"
    echo -e "${YELLOW}  cd llama.cpp && mkdir build && cd build && cmake .. && make -j4${NC}"
    exit 1
fi

# Check if port is already in use
if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null ; then
    echo -e "${RED}❌ Error: Port $PORT is already in use${NC}"
    echo -e "${YELLOW}Please stop the existing server or use a different port.${NC}"
    echo -e "${YELLOW}You can use stopLLM.sh to stop the current server.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Starting server...${NC}"

# Start the server with optimized parameters
cd llama.cpp && ./build/bin/llama-server \
    --model "$MODEL_PATH" \
    --port $PORT \
    --host $HOST \
    -c $CONTEXT_SIZE \
    -n $MAX_PREDICT \
    -t $THREADS \
    -tb $THREADS_BATCH \
    -b $BATCH_SIZE \
    -ub $UBATCH_SIZE \
    -np $PARALLEL_SLOTS \
    $FLASH_ATTN \
    --log-disable \
    2>&1 | while IFS= read -r line; do
        # Color-code important log messages
        if [[ $line == *"server is listening"* ]]; then
            echo -e "${GREEN}✅ $line${NC}"
        elif [[ $line == *"model loaded"* ]]; then
            echo -e "${GREEN}✅ $line${NC}"
        elif [[ $line == *"offloaded"*"layers to GPU"* ]]; then
            echo -e "${BLUE}🔧 $line${NC}"
        elif [[ $line == *"flash_attn = 1"* ]]; then
            echo -e "${BLUE}⚡ Flash attention enabled${NC}"
        elif [[ $line == *"n_slots ="* ]]; then
            echo -e "${BLUE}🔧 $line${NC}"
        elif [[ $line == *"error"* ]] || [[ $line == *"Error"* ]]; then
            echo -e "${RED}❌ $line${NC}"
        else
            echo "$line"
        fi
    done

echo -e "${YELLOW}Server stopped.${NC}"
