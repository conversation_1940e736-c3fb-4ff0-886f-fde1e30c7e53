# Translation Workflow Scripts

Automated shell scripts for complete book translation workflow using llama.cpp and Gemma 27B-IT.

## 📚 Scripts Overview

### 🚀 `translateBook.sh` - Full Book Translation
Complete automated workflow for translating the entire book with progress tracking and error handling.

### 🧪 `translateTest.sh` - Quick Testing
Fast testing script for validating setup and trying small text samples.

### ⚙️ `startLLM.sh` / `stopLLM.sh` - Server Management
Start and stop llama.cpp server with optimized parameters.

### 🎯 `improve_samples.py` - Sample Improvement
Interactive tool for building and refining translation examples.

## 🚀 Quick Start

### Full Book Translation
```bash
# Translate the complete book (in.txt)
./translateBook.sh

# Output: translated_book.txt
```

### Quick Test
```bash
# Test with a small file
./translateTest.sh test_small.txt my_test.txt

# Test with default files
./translateTest.sh
```

## 📋 Full Book Translation Workflow

### Prerequisites
- ✅ `in.txt` - Your Russian source text
- ✅ `style.txt` - Translation style guide
- ✅ `sample_source.txt` / `sample_target.txt` - Translation examples (optional)
- ✅ Gemma 27B-IT model downloaded to `llama.cpp/` directory

### Automatic Process
1. **Validation** - Checks all required files exist
2. **Configuration Display** - Shows translation parameters
3. **User Confirmation** - Asks for approval before starting
4. **Server Management** - Starts llama.cpp server if needed
5. **Translation** - Runs optimized translation with progress tracking
6. **Results** - Shows completion time and output statistics
7. **Cleanup** - Stops server if it was started by the script

### Example Output
```
📚 BOOK TRANSLATION WORKFLOW
==============================

📋 TRANSLATION CONFIGURATION
Input file:      in.txt (4517 lines)
Style file:      style.txt
Output file:     translated_book.txt
Model:           gemma-2-27b-it
Chunk tokens:    2500
Workers:         4
Temperature:     0.3
Estimated time:  45m

Proceed with translation? (y/N): y

🚀 STARTING TRANSLATION
Translation started at 2024-01-15 14:30:00

Processing 18 chunks with 4 parallel workers...
████████████████████████████████████████ 100%

🎉 TRANSLATION COMPLETED SUCCESSFULLY!
Total time: 42m 15s
Output file: translated_book.txt (4520 lines, 2.1MB)
```

## 🧪 Quick Testing

### Basic Usage
```bash
# Test with specific files
./translateTest.sh input.txt output.txt

# Test with defaults (test_small.txt → test_output.txt)
./translateTest.sh
```

### Features
- ✅ **Fast setup** - Automatically starts/stops server
- ✅ **Quick validation** - Tests your configuration
- ✅ **Preview output** - Shows first 10 lines of translation
- ✅ **Performance timing** - Reports translation speed

### Example Output
```
🧪 QUICK TRANSLATION TEST
=========================
Input:  test_small.txt
Output: test_output.txt

✅ Server ready!
✅ Translation completed in 29s
✅ Output: test_output.txt (5 lines)

📖 PREVIEW:
----------
− Morning, boys! What, don't hear the birdies singing? − a woman's voice...
```

## ⚙️ Configuration Options

### `translateBook.sh` Configuration
Edit the script to customize:
```bash
CHUNK_TOKENS=2500      # Text tokens per chunk (2000-3000 optimal)
WORKERS=4              # Parallel workers (match server -np setting)
TEMPERATURE=0.3        # Translation creativity (0.1-0.5)
MODEL_NAME="gemma-2-27b-it"  # Model name
```

### File Paths
```bash
INPUT_FILE="in.txt"                    # Source Russian text
STYLE_FILE="style.txt"                 # Style guide
OUTPUT_FILE="translated_book.txt"      # Output file
SAMPLE_SOURCE_FILE="sample_source.txt" # Translation examples (optional)
SAMPLE_TARGET_FILE="sample_target.txt" # Translation examples (optional)
```

## 📊 Performance Optimization

### Recommended Settings

**For Maximum Quality:**
```bash
CHUNK_TOKENS=3000
WORKERS=2
TEMPERATURE=0.2
```

**For Balanced Speed/Quality:**
```bash
CHUNK_TOKENS=2500
WORKERS=4
TEMPERATURE=0.3
```

**For Maximum Speed:**
```bash
CHUNK_TOKENS=2000
WORKERS=6
TEMPERATURE=0.4
```

### Hardware Considerations

**Apple Silicon M1 Max (64GB):**
- ✅ **Optimal**: 4 workers, 2500 tokens
- ✅ **Memory usage**: ~17GB GPU + 8GB system
- ✅ **Speed**: ~25 seconds per chunk

**Lower Memory Systems:**
- 🔧 **Reduce workers**: Use 2 workers instead of 4
- 🔧 **Smaller chunks**: Use 2000 tokens instead of 2500
- 🔧 **Monitor usage**: Watch GPU memory during translation

## 🔧 Troubleshooting

### Common Issues

**"Server failed to start"**
```bash
# Check if model file exists
ls -la llama.cpp/gemma-2-27b-it-Q4_K_M.gguf

# Check server logs
cat llama_server.log

# Try manual start
./startLLM.sh
```

**"Translation failed"**
```bash
# Check Python dependencies
pip install openai tiktoken tqdm

# Test with smaller file first
./translateTest.sh

# Check debug logs
ls -la logs_llamacpp/
```

**"Out of memory"**
```bash
# Reduce workers and chunk size
WORKERS=2
CHUNK_TOKENS=1500

# Or use smaller model
# Download gemma-2-9b-it instead of 27b
```

### Performance Issues

**Translation too slow:**
- ✅ Increase workers (if you have memory)
- ✅ Reduce chunk size for better parallelization
- ✅ Check GPU utilization

**Translation quality poor:**
- ✅ Increase chunk size for better context
- ✅ Lower temperature (0.1-0.2)
- ✅ Add more translation samples
- ✅ Use `improve_samples.py` to refine examples

## 📁 Output Files

### Translation Output
- **`translated_book.txt`** - Main translation output
- **`logs_llamacpp/`** - Debug logs for each chunk
- **`llama_server.log`** - Server startup/shutdown logs

### Log Analysis
```bash
# Check translation progress
grep "Processing" logs_llamacpp/*.json

# Find problematic chunks
grep -l "error" logs_llamacpp/*.json

# Review specific chunk
cat logs_llamacpp/chunk5.req.json
```

## 🎯 Best Practices

### Before Full Translation
1. **Test setup**: Run `./translateTest.sh` first
2. **Improve samples**: Use `improve_samples.py` to build 20-30 examples
3. **Validate style**: Ensure `style.txt` contains all character names and tone guidance

### During Translation
1. **Monitor progress**: Watch the progress bar and timing
2. **Check memory**: Monitor GPU/system memory usage
3. **Quality spot-checks**: Review a few chunks during translation

### After Translation
1. **Review output**: Check character name consistency
2. **Quality assessment**: Read several random sections
3. **Refinement**: Use `improve_samples.py` to fix any issues found

## 🚀 Complete Workflow Example

```bash
# 1. Prepare samples (optional but recommended)
python3 improve_samples.py --max_iterations 20

# 2. Test setup
./translateTest.sh

# 3. Translate full book
./translateBook.sh

# 4. Review and refine
less translated_book.txt
python3 improve_samples.py --max_iterations 5

# 5. Re-translate if needed with improved samples
./translateBook.sh
```

This workflow provides a complete, automated solution for high-quality book translation with minimal manual intervention!
