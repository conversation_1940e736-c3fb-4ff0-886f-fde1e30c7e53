
@GGML_VARIABLES_EXPANDED@

@PACKAGE_INIT@

set_and_check(GGML_INCLUDE_DIR "@PACKAGE_GGML_INCLUDE_INSTALL_DIR@")
set_and_check(GGML_LIB_DIR "@PACKAGE_GGML_LIB_INSTALL_DIR@")
#set_and_check(GGML_BIN_DIR "@PACKAGE_GGML_BIN_INSTALL_DIR@")

find_package(Threads REQUIRED)

find_library(GGML_LIBRARY ggml
    REQUIRED
    HINTS ${GGML_LIB_DIR}
    NO_CMAKE_FIND_ROOT_PATH)

add_library(ggml::ggml UNKNOWN IMPORTED)
set_target_properties(ggml::ggml
    PROPERTIES
        IMPORTED_LOCATION "${GGML_LIBRARY}")

find_library(GGML_BASE_LIBRARY ggml-base
    REQUIRED
    HINTS ${GGML_LIB_DIR}
    NO_CMAKE_FIND_ROOT_PATH)

add_library(ggml::ggml-base UNKNOWN IMPORTED)
set_target_properties(ggml::ggml-base
    PROPERTIES
        IMPORTED_LOCATION "${GGML_BASE_LIBRARY}")

if (NOT GGML_SHARED_LIB)
    if (APPLE AND GGML_ACCELERATE)
        find_library(ACCELERATE_FRAMEWORK Accelerate REQUIRED)
        list(APPEND GGML_CPU_INTERFACE_LINK_LIBRARIES ${ACCELERATE_FRAMEWORK})
    endif()

    if (GGML_OPENMP)
        find_package(OpenMP REQUIRED)
        list(APPEND GGML_CPU_INTERFACE_LINK_LIBRARIES OpenMP::OpenMP_C OpenMP::OpenMP_CXX)
    endif()

    if (GGML_CPU_HBM)
        find_library(memkind memkind REQUIRED)
        list(APPEND GGML_CPU_INTERFACE_LINK_LIBRARIES memkind)
    endif()

    if (GGML_BLAS)
        find_package(BLAS REQUIRED)
        list(APPEND GGML_CPU_INTERFACE_LINK_LIBRARIES ${BLAS_LIBRARIES})
        list(APPEND GGML_CPU_INTERFACE_LINK_OPTIONS   ${BLAS_LINKER_FLAGS})
    endif()

    if (GGML_CUDA)
        find_package(CUDAToolkit REQUIRED)
    endif()

    if (GGML_METAL)
        find_library(FOUNDATION_LIBRARY Foundation REQUIRED)
        find_library(METAL_FRAMEWORK    Metal REQUIRED)
        find_library(METALKIT_FRAMEWORK MetalKit REQUIRED)

        list(APPEND GGML_METAL_INTERFACE_LINK_LIBRARIES
                    ${FOUNDATION_LIBRARY} ${METAL_FRAMEWORK} ${METALKIT_FRAMEWORK})
    endif()

    if (GGML_VULKAN)
        find_package(Vulkan REQUIRED)
        list(APPEND GGML_VULKAN_INTERFACE_LINK_LIBRARIES Vulkan::Vulkan)
    endif()

    if (GGML_HIP)
        find_package(hip     REQUIRED)
        find_package(hipblas REQUIRED)
        find_package(rocblas REQUIRED)
        list(APPEND GGML_HIP_INTERFACE_LINK_LIBRARIES hip::host roc::rocblas roc::hipblas)
    endif()

    if (GGML_SYCL)
        find_package(DNNL)
        if (${DNNL_FOUND} AND GGML_SYCL_TARGET STREQUAL "INTEL")
            list(APPEND GGML_SYCL_INTERFACE_LINK_LIBRARIES DNNL::dnnl)
        endif()
        if (WIN32)
            find_package(IntelSYCL REQUIRED)
            find_package(MKL       REQUIRED)
            list(APPEND GGML_SYCL_INTERFACE_LINK_LIBRARIES IntelSYCL::SYCL_CXX MKL::MKL MKL::MKL_SYCL)
        endif()
    endif()
endif()

set(_ggml_all_targets "")
foreach(_ggml_backend ${GGML_AVAILABLE_BACKENDS})
    string(REPLACE "-" "_" _ggml_backend_pfx "${_ggml_backend}")
    string(TOUPPER "${_ggml_backend_pfx}" _ggml_backend_pfx)

    find_library(${_ggml_backend_pfx}_LIBRARY ${_ggml_backend}
        REQUIRED
        HINTS ${GGML_LIB_DIR}
        NO_CMAKE_FIND_ROOT_PATH)

    message(STATUS "Found ${${_ggml_backend_pfx}_LIBRARY}")

    add_library(ggml::${_ggml_backend} UNKNOWN IMPORTED)
    set_target_properties(ggml::${_ggml_backend}
        PROPERTIES
            INTERFACE_INCLUDE_DIRECTORIES "${GGML_INCLUDE_DIR}"
            IMPORTED_LINK_INTERFACE_LANGUAGES "CXX"
            IMPORTED_LOCATION "${${_ggml_backend_pfx}_LIBRARY}"
            INTERFACE_COMPILE_FEATURES c_std_90
            POSITION_INDEPENDENT_CODE ON)

    string(REGEX MATCH "^ggml-cpu" is_cpu_variant "${_ggml_backend}")
    if(is_cpu_variant)
        list(APPEND GGML_CPU_INTERFACE_LINK_LIBRARIES "ggml::ggml-base")
        set_target_properties(ggml::${_ggml_backend}
           PROPERTIES
               INTERFACE_LINK_LIBRARIES "${GGML_CPU_INTERFACE_LINK_LIBRARIES}")

        if(GGML_CPU_INTERFACE_LINK_OPTIONS)
            set_target_properties(ggml::${_ggml_backend}
                PROPERTIES
                    INTERFACE_LINK_OPTIONS "${GGML_CPU_INTERFACE_LINK_OPTIONS}")
        endif()

    else()
        list(APPEND ${_ggml_backend_pfx}_INTERFACE_LINK_LIBRARIES "ggml::ggml-base")
        set_target_properties(ggml::${_ggml_backend}
            PROPERTIES
                INTERFACE_LINK_LIBRARIES "${${_ggml_backend_pfx}_INTERFACE_LINK_LIBRARIES}")

        if(${_ggml_backend_pfx}_INTERFACE_LINK_OPTIONS)
            set_target_properties(ggml::${_ggml_backend}
                PROPERTIES
                    INTERFACE_LINK_OPTIONS "${${_ggml_backend_pfx}_INTERFACE_LINK_OPTIONS}")
        endif()
    endif()

    list(APPEND _ggml_all_targets ggml::${_ggml_backend})
endforeach()

list(APPEND GGML_INTERFACE_LINK_LIBRARIES ggml::ggml-base "${_ggml_all_targets}")
set_target_properties(ggml::ggml
    PROPERTIES
        INTERFACE_LINK_LIBRARIES "${GGML_INTERFACE_LINK_LIBRARIES}")

add_library(ggml::all INTERFACE IMPORTED)
set_target_properties(ggml::all
    PROPERTIES
        INTERFACE_LINK_LIBRARIES "${_ggml_all_targets}")

check_required_components(ggml)
