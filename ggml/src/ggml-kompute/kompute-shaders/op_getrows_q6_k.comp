#version 450

#include "common.comp"

#define NL 16
#define BYTES_FOR_TYPE 4 /*bytes for float*/
#define SIZE_OF_BLOCK sizeof_block_q6_k

layout(local_size_x = 1) in;

layout (binding = 0) readonly buffer tensorInA { uint8_t inA[]; };
layout (binding = 1) readonly buffer tensorInB { int inB[]; };
layout (binding = 2) writeonly buffer tensorOut { float out_[]; };

layout (push_constant) uniform parameter {
    uint inAOff;
    uint inBOff;
    uint outOff;
    int ne00;
    int nb01;
    int nb1;
} pcs;

block_q6_k get_unaligned_block_q6_k(uint index) {
    block_q6_k fres;
    [[unroll]] for (uint it = 0; it != QK_K / 2; it++) {
        fres.ql[it] = inA[index + it];
    }
    [[unroll]] for (uint it = 0; it != QK_K / 4; it++) {
        fres.qh[it] = inA[index + QK_K/2 + it];
    }
    [[unroll]] for (uint it = 0; it != QK_K / 16; it++) {
        fres.scales[it] = int8_t(inA[index + QK_K/2 + QK_K/4 + it]);
    }
    fres.d = u8BufToFloat16(inA, index + QK_K/2 + QK_K/4 + QK_K/16);
    return fres;
}

mat4 dequantize_block(uint index, uint il) {
    const block_q6_k block = get_unaligned_block_q6_k(index);
    return dequantize_q6_k(block, il);
}

#include "op_getrows.comp"
