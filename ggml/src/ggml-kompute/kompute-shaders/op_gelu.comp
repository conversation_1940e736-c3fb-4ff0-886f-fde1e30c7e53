#version 450

#include "common.comp"

layout(local_size_x = 1) in;

layout(binding = 0) buffer restrict readonly tensorIn { float in_[]; };
layout(binding = 1) buffer restrict writeonly tensorOut { float out_[]; };
layout(push_constant) uniform PushConstants {
    uint inOff;
    uint outOff;
} pcs;

void main() {
    const uint baseIndex = gl_WorkGroupID.x * 8;

    for (uint x = 0; x < 8; x++) {
        const uint i = baseIndex + x;
        const float y = in_[i + pcs.inOff];
        out_[i + pcs.outOff] = 0.5*y*(1.0 + tanh(clamp(SQRT_2_OVER_PI*y*(1.0 + GELU_COEF_A*y*y), -15.0, 15.0)));
    }
}
