#!/usr/bin/env python3
"""
improve_samples.py - Interactive translation sample improvement tool

This script helps you build and refine translation examples by:
1. Picking random sentences from the source text
2. Translating them using the current llama.cpp setup
3. Allowing you to approve or provide alternative translations
4. Adding approved pairs to sample_source.txt and sample_target.txt

This builds a curated translation memory that improves consistency and style.

Usage:
    python3 improve_samples.py --source in.txt --style style.txt [options]

Prerequisites:
    - llama.cpp server running (use ./startLLM.sh)
    - translate3_llamacpp.py working correctly
"""

import argparse
import random
import sys
import textwrap
from pathlib import Path
from typing import List, Tuple, Optional
import re
import threading
import queue
import time
from dataclasses import dataclass

# Import translation functionality from our existing script
try:
    from translate3_llamacpp import get_client, _translate_chunk
except ImportError:
    print("❌ Error: Could not import translation functions from translate3_llamacpp.py")
    print("Make sure translate3_llamacpp.py is in the same directory.")
    sys.exit(1)

# Colors for better CLI experience
class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    CYAN = '\033[96m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'

@dataclass
class TranslationTask:
    """Represents a translation task with source sentence and result."""
    sentence: str
    translation: Optional[str] = None
    error: Optional[str] = None
    completed: bool = False

def print_colored(text: str, color: str = Colors.END) -> None:
    """Print text with color."""
    print(f"{color}{text}{Colors.END}")

class BackgroundTranslator:
    """Handles background translation of sentences."""

    def __init__(self, style: str, examples: list[dict], model: str = "gemma-2-27b-it"):
        self.style = style
        self.examples = examples
        self.model = model
        self.task_queue = queue.Queue()
        self.result_queue = queue.Queue()
        self.worker_thread = None
        self.stop_event = threading.Event()
        self.running = False

    def start(self):
        """Start the background translation worker."""
        if self.running:
            return

        self.running = True
        self.stop_event.clear()
        self.worker_thread = threading.Thread(target=self._worker, daemon=True)
        self.worker_thread.start()
        print_colored("🚀 Background translator started", Colors.GREEN)

    def stop(self):
        """Stop the background translation worker."""
        if not self.running:
            return

        self.running = False
        self.stop_event.set()
        if self.worker_thread:
            self.worker_thread.join(timeout=2.0)
        print_colored("⏹️  Background translator stopped", Colors.YELLOW)

    def add_task(self, sentence: str) -> None:
        """Add a sentence to be translated in the background."""
        if not self.running:
            return

        task = TranslationTask(sentence=sentence)
        self.task_queue.put(task)

    def get_completed_task(self, timeout: float = 0.1) -> Optional[TranslationTask]:
        """Get a completed translation task if available."""
        try:
            return self.result_queue.get(timeout=timeout)
        except queue.Empty:
            return None

    def update_examples(self, examples: list[dict]) -> None:
        """Update the examples used for translation."""
        self.examples = examples

    def _worker(self):
        """Background worker that processes translation tasks."""
        while not self.stop_event.is_set():
            try:
                # Get a task with timeout to allow checking stop event
                task = self.task_queue.get(timeout=0.5)

                if self.stop_event.is_set():
                    break

                # Perform translation
                try:
                    translation = _translate_chunk(
                        chunk=task.sentence,
                        style=self.style,
                        examples=self.examples,
                        model=self.model,
                        max_resp_tokens=512,
                        temperature=0.3,
                        log_prefix=None
                    )
                    task.translation = translation.strip()
                except Exception as e:
                    task.error = str(e)

                task.completed = True
                self.result_queue.put(task)

            except queue.Empty:
                # No tasks available, continue checking stop event
                continue
            except Exception as e:
                print_colored(f"❌ Background translation error: {e}", Colors.RED)

def extract_sentences(text: str) -> List[str]:
    """Extract sentences from text, filtering out very short or very long ones."""
    # Split on sentence endings, but be careful with abbreviations
    sentences = re.split(r'[.!?]+\s+', text)
    
    # Clean and filter sentences
    cleaned_sentences = []
    for sentence in sentences:
        sentence = sentence.strip()
        if not sentence:
            continue
            
        # Skip very short sentences (likely fragments)
        if len(sentence.split()) < 5:
            continue
            
        # Skip very long sentences (likely paragraphs)
        if len(sentence.split()) > 50:
            continue
            
        # Skip sentences that are mostly punctuation or numbers
        if len(re.sub(r'[^\w\s]', '', sentence)) < 10:
            continue
            
        cleaned_sentences.append(sentence)
    
    return cleaned_sentences

def get_random_sentence(source_file: Path, existing_sources: set) -> str:
    """Get a random sentence from the source file that hasn't been used yet."""
    text = source_file.read_text(encoding='utf-8')
    sentences = extract_sentences(text)

    # Filter out sentences we've already used
    unused_sentences = [s for s in sentences if s not in existing_sources]

    if not unused_sentences:
        print_colored("⚠️  All sentences have been processed! Consider using a larger source file.", Colors.YELLOW)
        return random.choice(sentences)  # Fallback to any sentence

    return random.choice(unused_sentences)

def get_multiple_random_sentences(source_file: Path, existing_sources: set, count: int) -> List[str]:
    """Get multiple random sentences from the source file that haven't been used yet."""
    text = source_file.read_text(encoding='utf-8')
    sentences = extract_sentences(text)

    # Filter out sentences we've already used
    unused_sentences = [s for s in sentences if s not in existing_sources]

    if not unused_sentences:
        print_colored("⚠️  All sentences have been processed! Consider using a larger source file.", Colors.YELLOW)
        unused_sentences = sentences  # Fallback to any sentences

    # Return up to 'count' random sentences without replacement
    return random.sample(unused_sentences, min(count, len(unused_sentences)))

def load_existing_samples(sample_source_file: Path, sample_target_file: Path) -> tuple[set, list[dict]]:
    """Load existing sample sources to avoid duplicates and prepare examples for translation."""
    sources = set()
    examples = []

    if not sample_source_file.exists() or not sample_target_file.exists():
        return sources, examples

    source_content = sample_source_file.read_text(encoding='utf-8')
    target_content = sample_target_file.read_text(encoding='utf-8')

    # Parse source and target files to extract pairs
    source_texts = []
    target_texts = []

    # Extract source texts (skip numbered delimiter lines like "1:", "2:", etc.)
    for line in source_content.split('\n'):
        line = line.strip()
        if line and not re.match(r'^\d+:$', line) and not line.startswith('SAMPLE SOURCE:'):
            source_texts.append(line)
            sources.add(line)

    # Extract target texts (skip numbered delimiter lines like "1:", "2:", etc.)
    for line in target_content.split('\n'):
        line = line.strip()
        if line and not re.match(r'^\d+:$', line) and not line.startswith('SAMPLE TARGET:'):
            target_texts.append(line)

    # Create examples in the format expected by the translation function
    # Use the last few pairs as examples (limit to avoid context overflow)
    max_examples = 3  # Use last 3 pairs as examples
    for i in range(max(0, len(source_texts) - max_examples), len(source_texts)):
        if i < len(target_texts):
            examples.extend([
                {"role": "user", "content": f"SAMPLE SOURCE:\n{source_texts[i]}"},
                {"role": "assistant", "content": f"SAMPLE TARGET:\n{target_texts[i]}"}
            ])

    return sources, examples



def display_translation_pair(source: str, translation: str) -> None:
    """Display the source and translation in a nice format."""
    print("\n" + "="*80)
    print_colored("📖 ORIGINAL (Russian):", Colors.BLUE)
    print(textwrap.fill(source, width=76, initial_indent="  ", subsequent_indent="  "))
    print()
    print_colored("🔄 AI TRANSLATION (English):", Colors.CYAN)
    print(textwrap.fill(translation, width=76, initial_indent="  ", subsequent_indent="  "))
    print("="*80)

def get_user_choice() -> str:
    """Get user's choice: approve, edit, or skip."""
    while True:
        print_colored("\nWhat would you like to do?", Colors.YELLOW)
        print("  [a] Approve (translation is good, current guidelines sufficient)")
        print("  [e] Edit/provide alternative (add to guidelines)")
        print("  [s] Skip this sentence")
        print("  [q] Quit")

        choice = input("\nYour choice (a/e/s/q): ").lower().strip()

        if choice in ['a', 'e', 's', 'q']:
            return choice

        print_colored("Invalid choice. Please enter 'a', 'e', 's', or 'q'.", Colors.RED)

def get_alternative_translation() -> str:
    """Get alternative translation from user."""
    print_colored("\n✏️  Please provide your preferred translation:", Colors.GREEN)
    print("(Press Enter on empty line to finish, or type 'cancel' to cancel)")
    
    lines = []
    while True:
        line = input("  ")
        if line.lower() == 'cancel':
            return ""
        if not line:
            break
        lines.append(line)
    
    return ' '.join(lines).strip()

def get_next_sample_number(sample_file: Path) -> int:
    """Get the next sample number by counting existing numbered entries."""
    if not sample_file.exists():
        return 1

    content = sample_file.read_text(encoding='utf-8')
    # Count lines that match the pattern "number:"
    numbers = []
    for line in content.split('\n'):
        line = line.strip()
        match = re.match(r'^(\d+):$', line)
        if match:
            numbers.append(int(match.group(1)))

    return max(numbers, default=0) + 1

def append_sample_pair(source: str, target: str, sample_source_file: Path, sample_target_file: Path) -> None:
    """Append a new sample pair to the sample files using auto-increment numbers."""
    # Get the next sample number (should be the same for both files)
    next_number = max(
        get_next_sample_number(sample_source_file),
        get_next_sample_number(sample_target_file)
    )

    # Append to sample_source.txt with numbered delimiter
    with sample_source_file.open('a', encoding='utf-8') as f:
        f.write(f"\n{next_number}:\n{source}\n")

    # Append to sample_target.txt with numbered delimiter
    with sample_target_file.open('a', encoding='utf-8') as f:
        f.write(f"\n{next_number}:\n{target}\n")

def main():
    parser = argparse.ArgumentParser(
        description="Interactive tool to improve translation samples",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    parser.add_argument("--source", type=Path, default="in.txt", 
                       help="Source Russian text file")
    parser.add_argument("--style", type=Path, default="style.txt",
                       help="Style guide file")
    parser.add_argument("--sample_source", type=Path, default="sample_source.txt",
                       help="Sample source file to append to")
    parser.add_argument("--sample_target", type=Path, default="sample_target.txt", 
                       help="Sample target file to append to")
    parser.add_argument("--model", default="gemma-2-27b-it",
                       help="Model name for translation")
    parser.add_argument("--max_iterations", type=int, default=50,
                       help="Maximum number of sentences to process in one session")
    parser.add_argument("--buffer_size", type=int, default=3,
                       help="Number of translations to prepare in advance")

    args = parser.parse_args()
    
    # Validate files
    if not args.source.exists():
        print_colored(f"❌ Source file not found: {args.source}", Colors.RED)
        sys.exit(1)
    
    if not args.style.exists():
        print_colored(f"❌ Style file not found: {args.style}", Colors.RED)
        sys.exit(1)
    
    # Test connection to llama.cpp server
    try:
        client = get_client()
        models = client.models.list()
        print_colored(f"✅ Connected to llama.cpp server. Available models: {[m.id for m in models.data]}", Colors.GREEN)
    except Exception as e:
        print_colored(f"❌ Cannot connect to llama.cpp server: {e}", Colors.RED)
        print_colored("Make sure to start the server with: ./startLLM.sh", Colors.YELLOW)
        sys.exit(1)
    
    # Load style guide
    style_text = args.style.read_text(encoding='utf-8').strip()
    
    # Load existing samples to avoid duplicates and prepare examples
    existing_sources, examples = load_existing_samples(args.sample_source, args.sample_target)

    print_colored("🎯 Translation Sample Improvement Tool", Colors.HEADER + Colors.BOLD)
    print_colored("=" * 50, Colors.HEADER)
    print(f"Source file: {args.source}")
    print(f"Existing samples: {len(existing_sources)}")
    print(f"Max iterations: {args.max_iterations}")
    print(f"Buffer size: {args.buffer_size}")
    print()

    # Initialize background translator
    translator = BackgroundTranslator(style_text, examples, args.model)
    translator.start()

    # Prepare initial batch of sentences for translation
    print_colored("🔄 Preparing initial translations...", Colors.YELLOW)
    initial_sentences = get_multiple_random_sentences(args.source, existing_sources, args.buffer_size)
    for sentence in initial_sentences:
        translator.add_task(sentence)
        existing_sources.add(sentence)  # Mark as being processed

    approved_count = 0
    edited_count = 0
    skipped_count = 0
    pending_tasks = []  # Track sentences we've queued for translation
    
    try:
        for iteration in range(args.max_iterations):
            print_colored(f"\n🔄 Iteration {iteration + 1}/{args.max_iterations}", Colors.HEADER)

            # Wait for a completed translation
            current_task = None
            while current_task is None:
                current_task = translator.get_completed_task(timeout=0.5)
                if current_task is None:
                    print_colored("⏳ Waiting for translation to complete...", Colors.YELLOW)
                    time.sleep(0.5)

            # Check if translation was successful
            if current_task.error:
                print_colored(f"❌ Translation error: {current_task.error}", Colors.RED)
                # Queue another sentence to replace this failed one
                try:
                    new_sentence = get_random_sentence(args.source, existing_sources)
                    translator.add_task(new_sentence)
                    existing_sources.add(new_sentence)
                except Exception as e:
                    print_colored(f"❌ Error getting replacement sentence: {e}", Colors.RED)
                continue

            # Display the completed translation
            display_translation_pair(current_task.sentence, current_task.translation)

            # Queue the next sentence for background translation
            try:
                next_sentence = get_random_sentence(args.source, existing_sources)
                translator.add_task(next_sentence)
                existing_sources.add(next_sentence)
            except Exception as e:
                print_colored(f"❌ Error queuing next sentence: {e}", Colors.RED)
        
            # Get user choice
            choice = get_user_choice()

            if choice == 'q':
                print_colored("\n👋 Goodbye!", Colors.GREEN)
                break
            elif choice == 's':
                print_colored("⏭️  Skipped.", Colors.YELLOW)
                skipped_count += 1
                continue
            elif choice == 'a':
                # Approve the translation - don't add to samples since current guidelines are sufficient
                # Note: sentence already added to existing_sources when queued
                approved_count += 1
                print_colored("✅ Translation approved! Current guidelines are working well.", Colors.GREEN)
            elif choice == 'e':
                # Get alternative translation and add to guidelines
                alternative = get_alternative_translation()
                if alternative:
                    append_sample_pair(current_task.sentence, alternative, args.sample_source, args.sample_target)
                    # Update examples for next iterations in this session
                    examples.extend([
                        {"role": "user", "content": f"SAMPLE SOURCE:\n{current_task.sentence}"},
                        {"role": "assistant", "content": f"SAMPLE TARGET:\n{alternative}"}
                    ])
                    # Keep only recent examples to avoid context overflow
                    # if len(examples) > 6:  # Keep last 3 pairs (6 messages)
                    #     examples = examples[-6:]
                    # Update the background translator with new examples
                    translator.update_examples(examples)
                    edited_count += 1
                    print_colored("✅ Your translation added to guidelines!", Colors.GREEN)
                else:
                    print_colored("❌ Cancelled.", Colors.YELLOW)
                    continue
    
    finally:
        # Always stop the background translator
        translator.stop()

    # Summary
    print_colored(f"\n📊 SESSION SUMMARY", Colors.HEADER + Colors.BOLD)
    print_colored("=" * 30, Colors.HEADER)
    print(f"✅ Approved: {approved_count}")
    print(f"✏️  Edited: {edited_count}")
    print(f"⏭️  Skipped: {skipped_count}")
    print(f"📝 New guidelines added: {edited_count}")
    print(f"📚 Total guidelines in database: {len(existing_sources)}")

    if edited_count > 0:
        print_colored(f"\n🎉 Great work! Your translation guidelines have been improved.", Colors.GREEN)
        print_colored(f"These will help maintain consistency in future translations.", Colors.GREEN)
    elif approved_count > 0:
        print_colored(f"\n✅ Good news! Your current guidelines are working well.", Colors.GREEN)
        print_colored(f"The AI translations matched your expectations.", Colors.GREEN)

if __name__ == "__main__":
    main()
