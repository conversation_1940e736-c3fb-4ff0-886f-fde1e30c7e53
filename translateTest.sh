#!/bin/bash

# translateTest.sh - Quick test translation workflow
# For testing with smaller files before running the full book

# Configuration
INPUT_FILE="${1:-test_small.txt}"
OUTPUT_FILE="${2:-test_output.txt}"
STYLE_FILE="style.txt"
SAMPLE_SOURCE_FILE="sample_source.txt"
SAMPLE_TARGET_FILE="sample_target.txt"
MODEL_NAME="gemma-2-27b-it"
CHUNK_TOKENS=2000
WORKERS=2
TEMPERATURE=0.3

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')] $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check if server is running
check_server() {
    curl -s http://localhost:8080/v1/models >/dev/null 2>&1
    return $?
}

echo "🧪 QUICK TRANSLATION TEST"
echo "========================="
echo "Input:  $INPUT_FILE"
echo "Output: $OUTPUT_FILE"
echo ""

# Validate input file
if [ ! -f "$INPUT_FILE" ]; then
    print_error "Input file '$INPUT_FILE' not found!"
    echo "Usage: $0 [input_file] [output_file]"
    echo "Example: $0 test_small.txt my_test.txt"
    exit 1
fi

# Check if server is running
print_status "Checking server status..."
if ! check_server; then
    print_status "Starting server..."
    ./startLLM.sh > /dev/null 2>&1 &
    
    # Wait for server
    for i in {1..15}; do
        if check_server; then
            print_success "Server ready!"
            break
        fi
        echo -n "."
        sleep 2
    done
    
    if ! check_server; then
        print_error "Server failed to start"
        exit 1
    fi
    
    SERVER_STARTED=true
else
    print_success "Server already running"
    SERVER_STARTED=false
fi

# Build sample arguments
SAMPLE_ARGS=""
if [ -f "$SAMPLE_SOURCE_FILE" ] && [ -f "$SAMPLE_TARGET_FILE" ]; then
    SAMPLE_ARGS="--sample_source $SAMPLE_SOURCE_FILE --sample_target $SAMPLE_TARGET_FILE"
fi

# Run translation
print_status "Running translation..."
START_TIME=$(date +%s)

python3 translate3_llamacpp.py \
    --txt "$INPUT_FILE" \
    --style "$STYLE_FILE" \
    --output_txt "$OUTPUT_FILE" \
    $SAMPLE_ARGS \
    --model "$MODEL_NAME" \
    --chunk_tokens $CHUNK_TOKENS \
    --workers $WORKERS \
    --temperature $TEMPERATURE

RESULT=$?
END_TIME=$(date +%s)
ELAPSED=$((END_TIME - START_TIME))

if [ $RESULT -eq 0 ]; then
    print_success "Translation completed in ${ELAPSED}s"
    
    if [ -f "$OUTPUT_FILE" ]; then
        LINES=$(wc -l < "$OUTPUT_FILE")
        print_success "Output: $OUTPUT_FILE ($LINES lines)"
        
        echo ""
        echo "📖 PREVIEW:"
        echo "----------"
        head -n 10 "$OUTPUT_FILE"
        if [ $LINES -gt 10 ]; then
            echo "... (showing first 10 lines of $LINES total)"
        fi
    fi
else
    print_error "Translation failed"
fi

# Stop server if we started it
if [ "$SERVER_STARTED" = true ]; then
    print_status "Stopping server..."
    ./stopLLM.sh > /dev/null 2>&1
fi

exit $RESULT
