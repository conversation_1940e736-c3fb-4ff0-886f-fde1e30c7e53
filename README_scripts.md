# llama.cpp Server Management Scripts

Easy-to-use shell scripts for starting and stopping the llama.cpp server with optimized parameters for translation tasks.

## Scripts Overview

### 🚀 `startLLM.sh`
Starts the llama.cpp server with optimized parameters for Apple Silicon M1 Max and Gemma 2 27B-IT model.

### 🛑 `stopLLM.sh`
Gracefully stops all running llama-server processes.

## Quick Start

```bash
# Make scripts executable (one-time setup)
chmod +x startLLM.sh stopLLM.sh

# Start the server
./startLLM.sh

# Stop the server
./stopLLM.sh
```

## Optimized Configuration

The `startLLM.sh` script uses the following optimized parameters based on extensive testing:

```bash
# Core Parameters
-c 4096                    # Context window (4K tokens)
-n 512                     # Max prediction tokens
-np 4                      # 4 parallel slots for concurrent requests

# Apple Silicon Optimization
-t 8 -tb 8                 # Use all 8 P-cores
--flash-attn               # Enable flash attention for efficiency

# Batching Optimization
-b 2048 -ub 512           # Optimized batch sizes

# Network
--port 8080               # Standard port
--host 0.0.0.0           # Accept connections from any IP
```

## Features

### ✅ `startLLM.sh` Features:
- **Automatic validation**: Checks for model file and binary existence
- **Port conflict detection**: Prevents starting if port 8080 is in use
- **Colored output**: Easy-to-read status messages
- **Progress monitoring**: Shows key startup milestones
- **Error handling**: Clear error messages with solutions

### ✅ `stopLLM.sh` Features:
- **Graceful shutdown**: Attempts SIGTERM first, then SIGKILL if needed
- **Process detection**: Finds all llama-server processes automatically
- **Port verification**: Confirms port 8080 is released
- **Status reporting**: Clear feedback on shutdown progress

## Usage Examples

### Basic Usage
```bash
# Start server
./startLLM.sh

# Run translation
python3 translate3_llamacpp.py \
  --txt document.txt \
  --style style.txt \
  --output_txt output.txt \
  --sample_source sample_source.txt \
  --sample_target sample_target.txt

# Stop server
./stopLLM.sh
```

### Background Operation
```bash
# Start server in background
nohup ./startLLM.sh > llama_server.log 2>&1 &

# Check if running
curl http://localhost:8080/v1/models

# Stop when done
./stopLLM.sh
```

## Configuration

### Model Path
The script expects the model file at:
```
llama.cpp/gemma-2-27b-it-Q4_K_M.gguf
```

To use a different model, edit the `MODEL_PATH` variable in `startLLM.sh`:
```bash
MODEL_PATH="your-model-name.gguf"
```

### Port Configuration
To use a different port, edit the `PORT` variable in `startLLM.sh`:
```bash
PORT=8081
```

And update your translation script accordingly:
```bash
export LLAMACPP_BASE_URL="http://localhost:8081/v1"
```

## Troubleshooting

### Common Issues

**"Model file not found"**
```bash
# Ensure model is in the correct location
ls -la llama.cpp/gemma-2-27b-it-Q4_K_M.gguf

# Or update MODEL_PATH in startLLM.sh
```

**"Port 8080 is already in use"**
```bash
# Stop existing server
./stopLLM.sh

# Or find what's using the port
lsof -i :8080

# Or use a different port in startLLM.sh
```

**"llama-server binary not found"**
```bash
# Build llama.cpp
cd llama.cpp
mkdir build && cd build
cmake .. && make -j4
```

### Performance Monitoring

Monitor server performance:
```bash
# Check GPU usage (macOS)
sudo powermetrics --samplers gpu_power -n 1

# Check memory usage
top -pid $(pgrep llama-server)

# Check server logs
tail -f llama_server.log
```

## Performance Expectations

With the optimized configuration on Apple Silicon M1 Max:

- **Startup time**: ~30 seconds
- **Memory usage**: ~17GB (model + KV cache)
- **Translation speed**: ~38 seconds per 800-token chunk
- **Concurrent requests**: Up to 4 simultaneous translations
- **Quality**: Excellent (⭐⭐⭐⭐⭐)

## Integration with Translation Script

The scripts are designed to work seamlessly with `translate3_llamacpp.py`:

```bash
# Complete workflow
./startLLM.sh

# Wait for "server is listening" message, then:
python3 translate3_llamacpp.py \
  --txt large_document.txt \
  --style style.txt \
  --output_txt translated.txt \
  --sample_source sample_source.txt \
  --sample_target sample_target.txt \
  --workers 4 \
  --chunk_tokens 800

./stopLLM.sh
```

## Advanced Usage

### Custom Parameters
Edit `startLLM.sh` to modify parameters:
```bash
# For larger context (more memory usage)
CONTEXT_SIZE=8192

# For more parallel processing
PARALLEL_SLOTS=8

# For different threading
THREADS=4  # Use fewer cores
```

### Multiple Models
Create separate scripts for different models:
```bash
cp startLLM.sh startLLM_7B.sh
# Edit MODEL_PATH and PORT in startLLM_7B.sh
```

The scripts provide a robust, user-friendly way to manage your llama.cpp translation server!
